"""
Supabase Security Middleware

This middleware provides comprehensive security controls for Supabase integration,
including enhanced authentication, rate limiting, and audit logging.
"""

import logging
import time
from django.conf import settings
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.core.exceptions import PermissionDenied
from rest_framework.exceptions import Throttled, AuthenticationFailed
from apps.core.security import security_manager, audit_logger

logger = logging.getLogger('cvflo.security')


class SupabaseSecurityMiddleware(MiddlewareMixin):
    """
    Middleware for Supabase-specific security controls
    
    This middleware:
    1. Validates database connection security
    2. Enhances JWT token validation
    3. Implements enhanced rate limiting
    4. Logs security events
    5. Blocks suspicious activities
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.protected_paths = [
            '/api/cv/',
            '/api/pdf/',
            '/api/auth/',
        ]
        self.high_security_paths = [
            '/api/pdf/generate',
            '/api/admin/',
        ]
        
        # Validate security configuration on startup
        try:
            security_manager.validate_database_connection_security()
        except PermissionDenied as e:
            logger.critical(f'Security validation failed: {str(e)}')
            # In production, this should prevent startup
            if not hasattr(settings, 'TESTING'):
                raise
    
    def process_request(self, request: HttpRequest) -> HttpResponse:
        """
        Process incoming request for security validation
        
        Args:
            request: HTTP request object
            
        Returns:
            HttpResponse: If request should be blocked, None otherwise
        """
        try:
            # Skip security checks for certain paths
            if self._should_skip_security_check(request):
                return None
            
            # Extract user information if available
            user_id = getattr(request, 'user_id', None)
            if not user_id and hasattr(request, 'user'):
                user_id = getattr(request.user, 'username', None)
            
            # Enhanced security for protected paths  
            if self._is_protected_path(request.path):
                return self._handle_protected_request(request, user_id)
            
            return None
            
        except Throttled as e:
            return JsonResponse(
                {'error': 'Rate limit exceeded', 'detail': str(e)}, 
                status=429
            )
        except AuthenticationFailed as e:
            return JsonResponse(
                {'error': 'Authentication failed', 'detail': str(e)}, 
                status=401
            )
        except PermissionDenied as e:
            return JsonResponse(
                {'error': 'Permission denied', 'detail': str(e)}, 
                status=403
            )
        except Exception as e:
            logger.error(f'Security middleware error: {str(e)}')
            return JsonResponse(
                {'error': 'Security validation failed'}, 
                status=500
            )
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """
        Process outgoing response for audit logging
        
        Args:
            request: HTTP request object
            response: HTTP response object
            
        Returns:
            HttpResponse: Modified response
        """
        try:
            # Log successful operations on protected paths
            if self._is_protected_path(request.path) and 200 <= response.status_code < 300:
                user_id = getattr(request, 'user_id', 'anonymous')
                action = self._get_action_from_method(request.method)
                resource = request.path
                
                audit_logger.log_data_access(user_id, action, resource, request)
            
            # Log failed authentication attempts
            elif response.status_code in [401, 403]:
                user_id = getattr(request, 'user_id', 'unknown')
                audit_logger.log_authentication(
                    user_id, 
                    False, 
                    request, 
                    f'HTTP {response.status_code}'
                )
            
            return response
            
        except Exception as e:
            logger.error(f'Response processing error: {str(e)}')
            return response
    
    def _should_skip_security_check(self, request: HttpRequest) -> bool:
        """
        Determine if security check should be skipped
        
        Args:
            request: HTTP request object
            
        Returns:
            bool: True if security check should be skipped
        """
        # Skip for health checks and static files
        skip_paths = [
            '/health',
            '/static/',
            '/media/',
            '/admin/jsi18n/',
        ]
        
        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return True
        
        # Skip for OPTIONS requests (CORS preflight)
        if request.method == 'OPTIONS':
            return True
        
        return False
    
    def _is_protected_path(self, path: str) -> bool:
        """
        Check if path requires protection
        
        Args:
            path: URL path
            
        Returns:
            bool: True if path is protected
        """
        for protected_path in self.protected_paths:
            if path.startswith(protected_path):
                return True
        return False
    
    def _is_high_security_path(self, path: str) -> bool:
        """
        Check if path requires high security
        
        Args:
            path: URL path
            
        Returns:
            bool: True if path requires high security
        """
        for hs_path in self.high_security_paths:
            if path.startswith(hs_path):
                return True
        return False
    
    def _handle_protected_request(self, request: HttpRequest, user_id: str) -> HttpResponse:
        """
        Handle request to protected path
        
        Args:
            request: HTTP request object
            user_id: User identifier
            
        Returns:
            HttpResponse: If request should be blocked, None otherwise
        """
        # Check authentication is present
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth_header and not getattr(request, 'user', None):
            return JsonResponse(
                {'error': 'Authentication required'}, 
                status=401
            )
        
        # Enhanced rate limiting for protected paths
        action = 'api_call'
        if request.path.startswith('/api/pdf/'):
            action = 'pdf_generation'
        elif request.method == 'POST' and 'auth' in request.path:
            action = 'auth_attempt'
        
        # Apply rate limiting
        if user_id:
            security_manager.check_rate_limits(request, user_id, action)
        
        # Enhanced security for high-security paths
        if self._is_high_security_path(request.path):
            return self._handle_high_security_request(request, user_id)
        
        return None
    
    def _handle_high_security_request(self, request: HttpRequest, user_id: str) -> HttpResponse:
        """
        Handle request to high-security path
        
        Args:
            request: HTTP request object
            user_id: User identifier
            
        Returns:
            HttpResponse: If request should be blocked, None otherwise
        """
        # Additional validation for high-security paths
        
        # Check for failed login attempts
        client_ip = security_manager._get_client_ip(request)
        
        if not security_manager.check_failed_login_attempts(user_id, 'user'):
            audit_logger.log_security_event(
                'account_locked', user_id, request, 'high'
            )
            return JsonResponse(
                {'error': 'Account temporarily locked due to failed attempts'}, 
                status=429
            )
        
        if not security_manager.check_failed_login_attempts(client_ip, 'ip'):
            audit_logger.log_security_event(
                'ip_blocked', user_id, request, 'high'
            )
            return JsonResponse(
                {'error': 'IP temporarily blocked due to suspicious activity'}, 
                status=429
            )
        
        # Enhanced JWT validation for high-security paths
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]
            try:
                # In development, skip signature verification if JWT secret is not properly configured
                # This allows development to continue while using Supabase API validation
                from django.conf import settings
                verify_signature = not settings.DEBUG or (
                    hasattr(settings, 'SUPABASE_JWT_SECRET') and
                    settings.SUPABASE_JWT_SECRET and
                    settings.SUPABASE_JWT_SECRET != 'cvflo_for_you'  # Default placeholder value
                )

                # Validate with signature verification (disabled in dev if JWT secret not configured)
                security_manager.validate_supabase_jwt(token, verify_signature=verify_signature)
            except AuthenticationFailed as e:
                # Record failed attempt
                security_manager.record_failed_login(user_id, 'user')
                security_manager.record_failed_login(client_ip, 'ip')
                raise e
        
        return None
    
    def _get_action_from_method(self, method: str) -> str:
        """
        Get action name from HTTP method
        
        Args:
            method: HTTP method
            
        Returns:
            str: Action name
        """
        method_map = {
            'GET': 'READ',
            'POST': 'CREATE', 
            'PUT': 'UPDATE',
            'PATCH': 'UPDATE',
            'DELETE': 'DELETE',
        }
        return method_map.get(method, 'UNKNOWN')


class SupabaseAuditMiddleware(MiddlewareMixin):
    """
    Middleware for comprehensive audit logging of Supabase operations
    """
    
    def process_request(self, request: HttpRequest):
        """
        Log incoming requests to audited endpoints
        
        Args:
            request: HTTP request object
        """
        # Set request start time for performance monitoring
        request._audit_start_time = time.time()
        
        # Log high-value operations
        if self._should_audit_request(request):
            user_id = getattr(request, 'user_id', 'anonymous')
            
            audit_data = {
                'event_type': 'api_request',
                'user_id': user_id,
                'method': request.method,
                'path': request.path,
                'ip_address': security_manager._get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
                'content_length': request.META.get('CONTENT_LENGTH', 0),
            }
            
            logger.info(f'API request: {audit_data}')
    
    def process_response(self, request: HttpRequest, response: HttpResponse):
        """
        Log response details for audited endpoints
        
        Args:
            request: HTTP request object
            response: HTTP response object
            
        Returns:
            HttpResponse: Response object
        """
        if self._should_audit_request(request):
            duration = time.time() - getattr(request, '_audit_start_time', 0)
            user_id = getattr(request, 'user_id', 'anonymous')
            
            audit_data = {
                'event_type': 'api_response',
                'user_id': user_id,
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'response_size': len(response.content) if hasattr(response, 'content') else 0,
                'duration_ms': round(duration * 1000, 2),
            }
            
            # Log level based on status code
            if response.status_code >= 500:
                logger.error(f'API error response: {audit_data}')
            elif response.status_code >= 400:
                logger.warning(f'API client error: {audit_data}')
            else:
                logger.info(f'API response: {audit_data}')
        
        return response
    
    def _should_audit_request(self, request: HttpRequest) -> bool:
        """
        Determine if request should be audited
        
        Args:
            request: HTTP request object
            
        Returns:
            bool: True if request should be audited
        """
        # Audit all API calls
        audit_paths = [
            '/api/',
        ]
        
        # Skip static files and health checks
        skip_paths = [
            '/static/',
            '/media/',
            '/health',
            '/admin/jsi18n/',
        ]
        
        for skip_path in skip_paths:
            if request.path.startswith(skip_path):
                return False
        
        for audit_path in audit_paths:
            if request.path.startswith(audit_path):
                return True
        
        return False