"""
Supabase Authentication Backend

This module provides Django authentication integration with Supabase,
equivalent to the Node.js Supabase authentication middleware.
"""

import logging
import json
from typing import Optional, Dict, Any
from datetime import datetime, timezone
from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import User
from django.conf import settings
from django.core.cache import cache
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from supabase import create_client, Client
import jwt
from jwt import DecodeError, ExpiredSignatureError, InvalidTokenError
import requests

logger = logging.getLogger('cvflo')


class SupabaseService:
    """
    Service class for Supabase operations
    Equivalent to the Node.js SupabaseService
    """
    
    def __init__(self):
        self.url = settings.SUPABASE_URL
        self.anon_key = settings.SUPABASE_ANON_KEY
        self.service_role_key = settings.SUPABASE_SERVICE_ROLE_KEY
        
        if not all([self.url, self.anon_key]):
            raise ValueError("Supabase URL and ANON_KEY must be configured")
        
        self.client: Client = create_client(self.url, self.anon_key)
        self.admin_client: Client = create_client(self.url, self.service_role_key) if self.service_role_key else None
    
    def verify_token(self, token: str) -> dict:
        """
        Verify JWT token with Supabase using multiple validation methods
        
        Args:
            token: JWT token to verify
            
        Returns:
            dict: User data from token
            
        Raises:
            AuthenticationFailed: If token is invalid
        """
        try:
            # First, try to decode JWT locally for quick validation (optional)
            local_validation_success = False
            if hasattr(settings, 'SUPABASE_JWT_SECRET') and settings.SUPABASE_JWT_SECRET:
                try:
                    decoded = jwt.decode(
                        token,
                        settings.SUPABASE_JWT_SECRET,
                        algorithms=['HS256'],
                        options={'verify_exp': True}
                    )
                    logger.debug('JWT decoded successfully using local secret')
                    local_validation_success = True
                except (DecodeError, ExpiredSignatureError, InvalidTokenError) as e:
                    logger.warning(f'Local JWT validation failed: {str(e)}')
                    # Don't raise here - fall back to Supabase API validation
                    local_validation_success = False
            
            # Check token cache to avoid repeated API calls
            cache_key = f'supabase_token_{hash(token)}'
            cached_user = cache.get(cache_key)
            if cached_user:
                logger.debug('Retrieved user data from cache')
                return cached_user
            
            # Verify token with Supabase API (fallback or primary method)
            if not local_validation_success:
                logger.debug('Using Supabase API for token validation')
            response = self.client.auth.get_user(token)
            
            if not response.user:
                raise AuthenticationFailed('Invalid token - no user found')
            
            user_data = {
                'id': response.user.id,
                'email': response.user.email,
                'user_metadata': response.user.user_metadata or {},
                'app_metadata': response.user.app_metadata or {},
                'phone': getattr(response.user, 'phone', None),
                'email_verified': getattr(response.user, 'email_confirmed_at', None) is not None,
                'created_at': getattr(response.user, 'created_at', None),
                'last_sign_in_at': getattr(response.user, 'last_sign_in_at', None),
            }
            
            # Cache the user data for 5 minutes to reduce API calls
            cache.set(cache_key, user_data, timeout=300)
            
            logger.info(f'Token verified successfully for user: {user_data["email"]}')
            return user_data
            
        except AuthenticationFailed:
            raise
        except Exception as e:
            logger.error(f'Token verification failed with unexpected error: {str(e)}')
            raise AuthenticationFailed('Invalid or expired token')
    
    def get_or_create_django_user(self, supabase_user: dict) -> User:
        """
        Get or create Django user from Supabase user data with enhanced synchronization
        
        Args:
            supabase_user: User data from Supabase
            
        Returns:
            User: Django user instance
        """
        try:
            supabase_id = supabase_user['id']
            email = supabase_user['email']
            user_metadata = supabase_user.get('user_metadata', {})
            
            # Check cache first for performance
            cache_key = f'django_user_{supabase_id}'
            cached_user_id = cache.get(cache_key)
            if cached_user_id:
                try:
                    user = User.objects.get(pk=cached_user_id)
                    logger.debug(f'Retrieved Django user from cache: {email}')
                    return user
                except User.DoesNotExist:
                    cache.delete(cache_key)
            
            # Try to find existing user by username (Supabase ID)
            user = None
            try:
                user = User.objects.get(username=supabase_id)
                logger.debug(f'Found existing Django user by username: {email}')
            except User.DoesNotExist:
                # Try to find by email and update username
                try:
                    user = User.objects.get(email=email)
                    user.username = supabase_id
                    user.save(update_fields=['username'])
                    logger.info(f'Updated existing user username to Supabase ID: {email}')
                except User.DoesNotExist:
                    pass
            
            # Create new user if not found
            if not user:
                # Extract name from metadata with better parsing
                full_name = user_metadata.get('full_name', '').strip()
                first_name = user_metadata.get('first_name', '').strip()
                last_name = user_metadata.get('last_name', '').strip()
                
                # Parse full_name if first/last names are not provided
                if not first_name and not last_name and full_name:
                    name_parts = full_name.split(' ', 1)
                    first_name = name_parts[0]
                    last_name = name_parts[1] if len(name_parts) > 1 else ''
                
                # Fallback to email username if no name provided
                if not first_name and not last_name:
                    email_username = email.split('@')[0]
                    first_name = email_username.replace('.', ' ').replace('_', ' ').title()
                
                user = User.objects.create_user(
                    username=supabase_id,
                    email=email,
                    first_name=first_name[:30],  # Django limit
                    last_name=last_name[:150],   # Django limit
                    is_active=supabase_user.get('email_verified', True),  # Activate only if email verified
                )
                
                logger.info(f'Created new Django user for Supabase user: {email}')
                
                # Trigger user profile sync if needed
                self.sync_user_profile(user, supabase_user)
            else:
                # Update existing user info if needed
                self.update_user_info(user, supabase_user)
            
            # Cache the user ID for faster lookups
            cache.set(cache_key, user.pk, timeout=600)  # 10 minutes
            
            return user
            
        except Exception as e:
            logger.error(f'Failed to get or create Django user: {str(e)}')
            raise AuthenticationFailed('Failed to authenticate user')
    
    def sync_user_profile(self, django_user: User, supabase_user: dict):
        """
        Sync Django user with UserProfile model and potentially with Supabase
        
        Args:
            django_user: Django User instance
            supabase_user: Supabase user data
        """
        try:
            from apps.cv_builder.models import UserProfile
            
            # Create or update UserProfile
            user_profile, created = UserProfile.objects.get_or_create(
                id=supabase_user['id'],
                defaults={
                    'email': supabase_user['email'],
                }
            )
            
            if not created and user_profile.email != supabase_user['email']:
                user_profile.email = supabase_user['email']
                user_profile.save(update_fields=['email', 'updated_at'])
            
            logger.debug(f'Synced user profile for: {supabase_user["email"]}')
            
        except Exception as e:
            logger.warning(f'Failed to sync user profile: {str(e)}')
    
    def update_user_info(self, django_user: User, supabase_user: dict):
        """
        Update Django user information from Supabase data if changed
        
        Args:
            django_user: Django User instance
            supabase_user: Supabase user data
        """
        try:
            updated_fields = []
            
            # Update email if changed
            if django_user.email != supabase_user['email']:
                django_user.email = supabase_user['email']
                updated_fields.append('email')
            
            # Update activity status based on email verification
            email_verified = supabase_user.get('email_verified', True)
            if django_user.is_active != email_verified:
                django_user.is_active = email_verified
                updated_fields.append('is_active')
            
            if updated_fields:
                django_user.save(update_fields=updated_fields)
                logger.info(f'Updated Django user fields {updated_fields} for: {django_user.email}')
                
        except Exception as e:
            logger.warning(f'Failed to update user info: {str(e)}')
    
    def validate_token_format(self, token: str) -> bool:
        """
        Validate JWT token format before processing
        
        Args:
            token: JWT token to validate
            
        Returns:
            bool: True if format is valid
        """
        if not token or not isinstance(token, str):
            return False
        
        # JWT should have 3 parts separated by dots
        parts = token.split('.')
        return len(parts) == 3 and all(part for part in parts)


class SupabaseAuthentication(BaseAuthentication):
    """
    DRF Authentication class for Supabase JWT tokens
    Equivalent to the Node.js requireSupabaseAuth middleware
    """
    
    keyword = 'Bearer'
    
    def __init__(self):
        self.supabase_service = SupabaseService()
    
    def authenticate(self, request):
        """
        Authenticate request using Supabase JWT token with enhanced error handling
        
        Args:
            request: Django request object
            
        Returns:
            tuple: (user, token) if authenticated, None otherwise
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        
        if not auth_header:
            return None
        
        try:
            token = self.extract_token(auth_header)
            if not token:
                return None
                
            # Validate token format
            if not self.supabase_service.validate_token_format(token):
                logger.warning('Invalid JWT token format received')
                raise AuthenticationFailed('Invalid token format')
            
            # Verify token with Supabase
            supabase_user = self.supabase_service.verify_token(token)
            
            # Get or create Django user
            django_user = self.supabase_service.get_or_create_django_user(supabase_user)
            
            # Check if user is active
            if not django_user.is_active:
                logger.warning(f'Inactive user attempted authentication: {django_user.email}')
                raise AuthenticationFailed('User account is inactive')
            
            # Store Supabase user data in request for access in views
            request.supabase_user = supabase_user
            request.user_id = supabase_user['id']  # For convenience
            
            # Log successful authentication
            logger.info(f'User authenticated successfully: {django_user.email}')
            
            return (django_user, token)
            
        except AuthenticationFailed:
            raise
        except Exception as e:
            logger.error(f'Unexpected authentication error: {str(e)}')
            raise AuthenticationFailed('Authentication failed')
    
    def extract_token(self, auth_header: str) -> Optional[str]:
        """
        Extract Bearer token from Authorization header
        
        Args:
            auth_header: Authorization header value
            
        Returns:
            str or None: Extracted token
        """
        if not auth_header:
            return None
        
        parts = auth_header.split(' ')
        if len(parts) != 2 or parts[0] != self.keyword:
            return None
        
        return parts[1]
    
    def authenticate_header(self, request):
        """
        Return authentication header for 401 responses
        """
        return self.keyword


class SupabaseBackend(BaseBackend):
    """
    Django authentication backend for Supabase
    Can be used for session-based authentication
    """
    
    def __init__(self):
        self.supabase_service = SupabaseService()
    
    def authenticate(self, request, token=None, **kwargs):
        """
        Authenticate using Supabase token
        
        Args:
            request: Django request object
            token: JWT token
            
        Returns:
            User or None: Django user if authenticated
        """
        if not token:
            return None
        
        try:
            supabase_user = self.supabase_service.verify_token(token)
            return self.supabase_service.get_or_create_django_user(supabase_user)
            
        except AuthenticationFailed:
            return None
    
    def get_user(self, user_id):
        """
        Get user by ID
        
        Args:
            user_id: User ID
            
        Returns:
            User or None: Django user instance
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None


class OptionalSupabaseAuthentication(SupabaseAuthentication):
    """
    Optional Supabase authentication - doesn't fail if no token provided
    Equivalent to the Node.js optionalSupabaseAuth middleware
    """
    
    def authenticate(self, request):
        """
        Authenticate request but don't fail if no token provided
        """
        try:
            result = super().authenticate(request)
            return result
        except AuthenticationFailed:
            # Return None for optional authentication instead of raising exception
            return None